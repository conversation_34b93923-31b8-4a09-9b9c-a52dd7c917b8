import passport from 'passport';
import { GitHubAuthProvider } from './providers/github.provider';
import { AzureAuthProvider } from './providers/azure.provider';
import { authConfig, validateAuthConfig } from '../config/auth';
import { logger } from '../utils/logger';
import { User } from '../types/api';

export interface AuthProviderInfo {
  name: string;
  loginUrl: string;
  icon: string;
  enabled: boolean;
}

// Shared user store for all providers
const userStore = new Map<string, User>();

export class AuthManager {
  private githubProvider: GitHubAuthProvider;
  private azureProvider: AzureAuthProvider;

  constructor() {
    this.githubProvider = new GitHubAuthProvider();
    this.azureProvider = new AzureAuthProvider();
  }

  initialize(): void {
    try {
      // Validate configuration first
      validateAuthConfig();

      logger.info('Initializing Authentication Manager', {
        provider: authConfig.provider,
        githubEnabled: authConfig.github.enabled,
        azureEnabled: authConfig.azure.enabled
      });

      // Configure passport serialization
      this.configurePassportSerialization();

      // Configure enabled providers
      if (authConfig.github.enabled) {
        this.githubProvider.configure();
      }

      if (authConfig.azure.enabled) {
        this.azureProvider.configure();
      }

      logger.info('Authentication Manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Authentication Manager:', error);
      throw error;
    }
  }

  private configurePassportSerialization(): void {
    passport.serializeUser((user: any, done) => {
      done(null, user.id);
    });

    passport.deserializeUser((id: string, done) => {
      try {
        // Get full user data from store
        const user = userStore.get(id);
        
        if (user) {
          done(null, user);
        } else {
          done(new Error('User not found'), null);
        }
      } catch (error) {
        logger.error('Error deserializing user:', error);
        done(error, null);
      }
    });
  }

  // Method to store user data
  storeUser(user: User): void {
    userStore.set(user.id, user);
  }

  // Method to get user data
  getUser(id: string): User | undefined {
    return userStore.get(id);
  }

  getEnabledProviders(): AuthProviderInfo[] {
    const providers: AuthProviderInfo[] = [];

    if (this.githubProvider.isEnabled()) {
      providers.push({
        name: this.githubProvider.getName(),
        loginUrl: this.githubProvider.getLoginUrl(),
        icon: this.githubProvider.getIcon(),
        enabled: true
      });
    }

    if (this.azureProvider.isEnabled()) {
      providers.push({
        name: this.azureProvider.getName(),
        loginUrl: this.azureProvider.getLoginUrl(),
        icon: this.azureProvider.getIcon(),
        enabled: true
      });
    }

    return providers;
  }

  getGitHubProvider(): GitHubAuthProvider {
    return this.githubProvider;
  }

  getAzureProvider(): AzureAuthProvider {
    return this.azureProvider;
  }

  isGitHubEnabled(): boolean {
    return this.githubProvider.isEnabled();
  }

  isAzureEnabled(): boolean {
    return this.azureProvider.isEnabled();
  }

  getCurrentProvider(): string {
    return authConfig.provider;
  }
}

// Create singleton instance
export const authManager = new AuthManager(); 