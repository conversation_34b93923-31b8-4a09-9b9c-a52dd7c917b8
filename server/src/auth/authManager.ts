import passport from 'passport';
import { PingFedAuthProvider } from './providers/pingfed.provider';
import { authConfig, validateAuthConfig } from '../config/auth';
import { logger } from '../utils/logger';
import { User } from '../types/api';

export interface AuthProviderInfo {
  name: string;
  loginUrl: string;
  icon: string;
  enabled: boolean;
}

// Shared user store for all providers
const userStore = new Map<string, User>();

export class AuthManager {
  private pingfedProvider: PingFedAuthProvider;

  constructor() {
    this.pingfedProvider = new PingFedAuthProvider();
  }

  initialize(): void {
    try {
      // Validate configuration first
      validateAuthConfig();

      logger.info('Initializing Authentication Manager', {
        provider: authConfig.provider,
        pingfedEnabled: authConfig.pingfed.enabled,
      });

      // Configure passport serialization
      this.configurePassportSerialization();

      // Configure enabled providers
      if (authConfig.pingfed.enabled) {
        this.pingfedProvider.configure();
      }

      logger.info('Authentication Manager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Authentication Manager:', error);
      throw error;
    }
  }

  private configurePassportSerialization(): void {
    passport.serializeUser((user: any, done) => {
      done(null, user.id);
    });

    passport.deserializeUser((id: string, done) => {
      try {
        // Get full user data from store
        const user = userStore.get(id);

        if (user) {
          done(null, user);
        } else {
          done(new Error('User not found'), null);
        }
      } catch (error) {
        logger.error('Error deserializing user:', error);
        done(error, null);
      }
    });
  }

  // Method to store user data
  storeUser(user: User): void {
    userStore.set(user.id, user);
  }

  // Method to get user data
  getUser(id: string): User | undefined {
    return userStore.get(id);
  }

  getEnabledProviders(): AuthProviderInfo[] {
    const providers: AuthProviderInfo[] = [];

    if (this.pingfedProvider.isEnabled()) {
      providers.push({
        name: this.pingfedProvider.getName(),
        loginUrl: this.pingfedProvider.getLoginUrl(),
        icon: this.pingfedProvider.getIcon(),
        enabled: true,
      });
    }

    return providers;
  }

  getPingFedProvider(): PingFedAuthProvider {
    return this.pingfedProvider;
  }

  isPingFedEnabled(): boolean {
    return this.pingfedProvider.isEnabled();
  }

  getCurrentProvider(): string {
    return authConfig.provider;
  }
}

// Create singleton instance
export const authManager = new AuthManager();
