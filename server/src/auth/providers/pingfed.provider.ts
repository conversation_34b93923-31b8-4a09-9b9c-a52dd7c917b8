import { authConfig } from '../../config/auth';
import { logger } from '../../utils/logger';
import { User, UserRole, AuthProvider } from '../../types/api';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

export class PingFedAuthProvider {
  private userStore = new Map<string, User>();
  private codeVerifierStore = new Map<string, string>();

  configure(): void {
    if (!authConfig.pingfed.enabled) {
      return;
    }

    logger.info('Configuring PingFed OAuth provider');
  }

  isEnabled(): boolean {
    return authConfig.pingfed.enabled;
  }

  getName(): string {
    return 'Sign In';
  }

  getLoginUrl(): string {
    return '/api/auth/pingfed';
  }

  getIcon(): string {
    return 'walmart';
  }

  // Generate PKCE code verifier
  private generateCodeVerifier(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  // Generate PKCE code challenge
  private generateCodeChallenge(codeVerifier: string): string {
    return crypto.createHash('sha256').update(codeVerifier).digest('base64url');
  }

  // Store code verifier for later use
  private storeCodeVerifier(state: string, codeVerifier: string): void {
    this.codeVerifierStore.set(state, codeVerifier);
  }

  // Retrieve stored code verifier
  private getStoredCodeVerifier(state?: string): string {
    if (!state) {
      throw new Error('State parameter is required for PKCE flow');
    }
    const codeVerifier = this.codeVerifierStore.get(state);
    if (!codeVerifier) {
      throw new Error('Code verifier not found for state');
    }
    // Clean up after use
    this.codeVerifierStore.delete(state);
    return codeVerifier;
  }

  // Generate authorization URL with PKCE
  getAuthorizationUrl(state?: string): string {
    if (!authConfig.pingfed.enabled) {
      throw new Error('PingFed provider not enabled');
    }

    const actualState = state || crypto.randomBytes(16).toString('hex');
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = this.generateCodeChallenge(codeVerifier);

    // Store code verifier for later use
    this.storeCodeVerifier(actualState, codeVerifier);

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: authConfig.pingfed.clientId,
      redirect_uri: authConfig.pingfed.callbackURL,
      scope: authConfig.pingfed.scopes.join(' '),
      state: actualState,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
    });

    return `${authConfig.pingfed.authorizationEndpoint}?${params.toString()}`;
  }

  // Handle OAuth callback and exchange code for tokens
  async handleCallback(code: string, state?: string): Promise<User> {
    if (!authConfig.pingfed.enabled) {
      throw new Error('PingFed provider not configured');
    }

    try {
      logger.info('PingFed OAuth callback received', { state });

      // Exchange authorization code for access token
      const tokenResponse = await this.exchangeCodeForToken(code, state);
      const { access_token } = tokenResponse;

      // Get user information
      const userInfo = await this.getUserInfo(access_token);

      // Create user object
      const user = this.createUserFromPingFedProfile(userInfo, access_token);

      // Store user in local store
      this.userStore.set(user.id, user);

      // Also store in auth manager (import it to avoid circular dependency)
      const { authManager } = await import('../authManager');
      authManager.storeUser(user);

      logger.info('PingFed OAuth authentication successful', {
        userId: user.id,
        email: user.email,
      });

      return user;
    } catch (error) {
      logger.error('PingFed OAuth callback error:', error);
      throw error;
    }
  }

  // Exchange authorization code for access token
  private async exchangeCodeForToken(code: string, state?: string): Promise<any> {
    const codeVerifier = this.getStoredCodeVerifier(state);

    const tokenData = {
      grant_type: 'authorization_code',
      client_id: authConfig.pingfed.clientId,
      client_secret: authConfig.pingfed.clientSecret,
      code: code,
      redirect_uri: authConfig.pingfed.callbackURL,
      code_verifier: codeVerifier,
    };

    const body = new URLSearchParams(tokenData).toString();

    logger.info('Exchanging authorization code for token', {
      tokenEndpoint: authConfig.pingfed.tokenEndpoint,
      clientId: authConfig.pingfed.clientId,
      redirectUri: authConfig.pingfed.callbackURL,
    });

    try {
      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Token exchange timeout after 5 seconds')), 5000);
      });

      const fetchPromise = fetch(authConfig.pingfed.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'wm_qos.correlation_id': Date.now().toString(),
          Accept: 'application/json',
        },
        body: body,
      });

      const response = (await Promise.race([fetchPromise, timeoutPromise])) as Response;

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('Token exchange failed', {
          status: response.status,
          statusText: response.statusText,
          errorText,
          tokenEndpoint: authConfig.pingfed.tokenEndpoint,
          clientId: authConfig.pingfed.clientId,
        });
        throw new Error(`Token exchange failed: ${response.statusText} - ${errorText}`);
      }

      const tokenResponse = await response.json();
      logger.info('Token exchange successful');
      return tokenResponse;
    } catch (error) {
      logger.error('Token exchange error:', error);
      throw error;
    }
  }

  // Get user information from PingFed
  private async getUserInfo(accessToken: string): Promise<any> {
    logger.info('Fetching user info from PingFed');

    // Try multiple methods to get user info as PingFed can be configured differently
    const methods = [
      {
        name: 'Authorization Header (Standard)',
        request: () =>
          fetch(authConfig.pingfed.userInfoEndpoint, {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${accessToken}`,
              'wm_qos.correlation_id': Date.now().toString(),
              Accept: 'application/json',
            },
          }),
      },
      {
        name: 'POST with access_token in body',
        request: () => {
          const userInfoParams = new URLSearchParams({
            access_token: accessToken,
          });
          return fetch(authConfig.pingfed.userInfoEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'wm_qos.correlation_id': Date.now().toString(),
              Accept: 'application/json',
            },
            body: userInfoParams.toString(),
          });
        },
      },
    ];

    for (const method of methods) {
      try {
        logger.info(`Trying user info method: ${method.name}`);
        const response = await method.request();

        if (response.ok) {
          const userInfo = (await response.json()) as any;
          logger.info(`User info retrieved successfully using: ${method.name}`, {
            userPrincipalName: userInfo.userPrincipalName,
          });
          return userInfo;
        } else {
          const errorText = await response.text();
          logger.warn(`Method ${method.name} failed:`, {
            status: response.status,
            statusText: response.statusText,
            errorText,
          });
        }
      } catch (error) {
        logger.warn(`Method ${method.name} error:`, error);
      }
    }

    throw new Error('Failed to retrieve user information from PingFed using all available methods');
  }

  // Create user object from PingFed profile
  private createUserFromPingFedProfile(userInfo: any, _accessToken: string): User {
    // Extract username and site information from userPrincipalName
    const [username] = userInfo?.userPrincipalName?.split('@') || [];
    const [userId, rawSiteId] = username?.split('.') || [];
    const siteId = rawSiteId?.replace(/^s0/, '') || rawSiteId;

    // Extract first and last name from display name or username
    const displayName = userInfo.displayName || userInfo.name || username || 'Unknown User';
    const nameParts = displayName.split(' ');
    const firstName = nameParts[0] || 'Unknown';
    const lastName = nameParts.slice(1).join(' ') || 'User';

    // Create user object with minimal profile information
    const user: User = {
      id: uuidv4(),
      email: userInfo.userPrincipalName || userInfo.email || `${userId}@walmart.com`,
      firstName: firstName,
      lastName: lastName,
      role: UserRole.USER,
      isActive: true,
      authProvider: AuthProvider.PINGFED,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),

      // PingFed specific fields
      pingfedId: userInfo.sub || userInfo.userPrincipalName || userId,
      userPrincipalName: userInfo.userPrincipalName,
      siteId: siteId,
      displayName: displayName,
    };

    logger.info('Created user from PingFed profile', {
      userId: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      siteId: siteId,
    });

    return user;
  }

  // Introspect token to check if it's still valid
  async introspectToken(accessToken: string): Promise<any> {
    try {
      const introspectionData = {
        token: accessToken,
        token_type_hint: 'access_token',
        client_id: authConfig.pingfed.clientId,
      };

      const body = new URLSearchParams(introspectionData).toString();

      const response = await fetch(authConfig.pingfed.introspectionEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'wm_qos.correlation_id': Date.now().toString(),
          Accept: 'application/json',
        },
        body: body,
      });

      if (!response.ok) {
        throw new Error(`Token introspection failed: ${response.statusText}`);
      }

      const introspectionResult = (await response.json()) as any;
      logger.info('Token introspection result', {
        active: introspectionResult.active,
        exp: introspectionResult.exp,
      });

      return introspectionResult;
    } catch (error) {
      logger.error('Token introspection error:', error);
      throw error;
    }
  }

  // Sign out user and revoke tokens
  async signOut(refreshToken?: string): Promise<void> {
    try {
      if (refreshToken) {
        const logoutData = {
          token: refreshToken,
          client_id: authConfig.pingfed.clientId,
          redirect_uri: authConfig.pingfed.callbackURL,
        };

        const body = new URLSearchParams(logoutData).toString();

        await fetch(authConfig.pingfed.logoutEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'wm_qos.correlation_id': Date.now().toString(),
            Accept: 'application/json',
          },
          body: body,
        });
      }

      logger.info('User signed out from PingFed');
    } catch (error) {
      logger.error('Sign out error:', error);
      // Don't throw error for logout failures
    }
  }

  // Get user by ID from store
  getUserById(id: string): User | undefined {
    return this.userStore.get(id);
  }

  // Clear user store (for testing/cleanup)
  clearUserStore(): void {
    this.userStore.clear();
    this.codeVerifierStore.clear();
  }
}
