import { Router, Request, Response } from 'express';
import passport from 'passport';
import { authManager } from '../auth/authManager';
import { logger } from '../utils/logger';
import { ResponseUtil } from '../utils/response';
import { config } from '../config';

const router = Router();

// Get available auth providers
router.get('/providers', (req: Request, res: Response) => {
  try {
    const providers = authManager.getEnabledProviders();
    ResponseUtil.success(res, { providers }, 'Available authentication providers');
  } catch (error) {
    logger.error('Error getting auth providers:', error);
    ResponseUtil.internalError(res, 'Failed to get authentication providers');
  }
});

// Check authentication status
router.get('/status', (req: Request, res: Response) => {
  try {
    const isAuthenticated = req.isAuthenticated && req.isAuthenticated();
    ResponseUtil.success(
      res,
      {
        authenticated: isAuthenticated,
        user: isAuthenticated ? req.user : null,
      },
      'Authentication status'
    );
  } catch (error) {
    logger.error('Error checking auth status:', error);
    ResponseUtil.internalError(res, 'Failed to check authentication status');
  }
});

// Logout
router.post('/logout', (req: Request, res: Response) => {
  try {
    req.logout((err) => {
      if (err) {
        logger.error('Logout error:', err);
        return ResponseUtil.internalError(res, 'Failed to logout');
      }

      // Clear session
      req.session.destroy((sessionErr) => {
        if (sessionErr) {
          logger.error('Session destruction error:', sessionErr);
        }
      });

      ResponseUtil.success(res, null, 'Logged out successfully');
    });
  } catch (error) {
    logger.error('Logout error:', error);
    ResponseUtil.internalError(res, 'Failed to logout');
  }
});

// PingFed OAuth routes (only enabled if PingFed is configured)
if (authManager.isPingFedEnabled()) {
  // PingFed OAuth login
  router.get('/pingfed', async (req: Request, res: Response) => {
    try {
      const state = (req.query.state as string) || Math.random().toString(36).substring(7);
      const authUrl = authManager.getPingFedProvider().getAuthorizationUrl(state);
      res.redirect(authUrl);
    } catch (error) {
      logger.error('PingFed OAuth initiation error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });

  // PingFed OAuth callback
  router.get('/pingfed/callback', async (req: Request, res: Response) => {
    try {
      const code = req.query.code as string;
      const state = req.query.state as string;

      if (!code) {
        logger.error('No code received in PingFed callback');
        return res.redirect(`${config.urls.frontend}?auth=error`);
      }

      // Handle the callback using PingFed provider
      const user = await authManager.getPingFedProvider().handleCallback(code, state);

      // Set user in session
      req.login(user, (err) => {
        if (err) {
          logger.error('Error setting user in session:', err);
          return res.redirect(`${config.urls.frontend}?auth=error`);
        }
        res.redirect(`${config.urls.frontend}?auth=success`);
      });
    } catch (error) {
      logger.error('PingFed auth callback error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });
}

// Test endpoint to simulate OAuth callback (for local testing only)
if (process.env.NODE_ENV === 'development') {
  router.get('/test-callback', async (req: Request, res: Response) => {
    try {
      // Create a mock user for testing
      const mockUser = {
        id: 'test-user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        displayName: 'Test User',
        authProvider: 'pingfed' as const,
        pingfedId: 'test-pingfed-123',
        userPrincipalName: '<EMAIL>',
        isActive: true,
        role: 'user' as const,
        createdAt: new Date().toISOString(),
      };

      // Set user in session
      req.login(mockUser, (err) => {
        if (err) {
          logger.error('Error setting test user in session:', err);
          return res.redirect(`${config.urls.frontend}?auth=error`);
        }
        logger.info('Test user logged in successfully');
        res.redirect(`${config.urls.frontend}?auth=success`);
      });
    } catch (error) {
      logger.error('Test callback error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });
}

export { router as authRoutes };
