import { Router, Request, Response } from 'express';
import passport from 'passport';
import { authManager } from '../auth/authManager';
import { logger } from '../utils/logger';
import { ResponseUtil } from '../utils/response';
import { config } from '../config';

const router = Router();

// Get available auth providers
router.get('/providers', (req: Request, res: Response) => {
  try {
    const providers = authManager.getEnabledProviders();
    ResponseUtil.success(res, { providers }, 'Available authentication providers');
  } catch (error) {
    logger.error('Error getting auth providers:', error);
    ResponseUtil.internalError(res, 'Failed to get authentication providers');
  }
});

// Check authentication status
router.get('/status', (req: Request, res: Response) => {
  try {
    const isAuthenticated = req.isAuthenticated && req.isAuthenticated();
    ResponseUtil.success(res, {
      authenticated: isAuthenticated,
      user: isAuthenticated ? req.user : null
    }, 'Authentication status');
  } catch (error) {
    logger.error('Error checking auth status:', error);
    ResponseUtil.internalError(res, 'Failed to check authentication status');
  }
});

// Logout
router.post('/logout', (req: Request, res: Response) => {
  try {
    req.logout((err) => {
      if (err) {
        logger.error('Logout error:', err);
        return ResponseUtil.internalError(res, 'Failed to logout');
      }
      
      // Clear session
      req.session.destroy((sessionErr) => {
        if (sessionErr) {
          logger.error('Session destruction error:', sessionErr);
        }
      });
      
      ResponseUtil.success(res, null, 'Logged out successfully');
    });
  } catch (error) {
    logger.error('Logout error:', error);
    ResponseUtil.internalError(res, 'Failed to logout');
  }
});

// GitHub OAuth routes (only enabled if GitHub is configured)
if (authManager.isGitHubEnabled()) {
  // GitHub OAuth login
  router.get('/github', 
    passport.authenticate('github', { scope: ['user:email'] })
  );

  // GitHub OAuth callback
  router.get('/github/callback', passport.authenticate('github', { 
    failureRedirect: `${config.urls.frontend}?auth=error` 
  }), (req, res) => {
    try {
      // Authentication successful
      res.redirect(`${config.urls.frontend}?auth=success`);
    } catch (error) {
      console.error('GitHub auth callback error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });
}

// Azure AD OAuth routes (only enabled if Azure is configured)
if (authManager.isAzureEnabled()) {
  // Azure AD OAuth login
  router.get('/azure', async (req: Request, res: Response) => {
    try {
      const state = req.query.state as string || Math.random().toString(36).substring(7);
      const authUrl = await authManager.getAzureProvider().getAuthCodeUrl(state);
      res.redirect(authUrl);
    } catch (error) {
      logger.error('Azure AD OAuth initiation error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });

  // Azure AD OAuth callback
  router.get('/azure/callback', async (req: Request, res: Response) => {
    try {
      const code = req.query.code as string;
      const state = req.query.state as string;

      if (!code) {
        logger.error('No code received in Azure AD callback');
        return res.redirect(`${config.urls.frontend}?auth=error`);
      }

      // Handle the callback using MSAL
      const user = await authManager.getAzureProvider().handleCallback(code, state);
      
      // Set user in session
      req.login(user, (err) => {
        if (err) {
          logger.error('Error setting user in session:', err);
          return res.redirect(`${config.urls.frontend}?auth=error`);
        }
      res.redirect(`${config.urls.frontend}?auth=success`);
      });
    } catch (error) {
      logger.error('Azure auth callback error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });
}

export { router as authRoutes }; 