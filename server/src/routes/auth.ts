import { Router, Request, Response } from 'express';
import passport from 'passport';
import { authManager } from '../auth/authManager';
import { logger } from '../utils/logger';
import { ResponseUtil } from '../utils/response';
import { config } from '../config';

const router = Router();

// Get available auth providers
router.get('/providers', (req: Request, res: Response) => {
  try {
    const providers = authManager.getEnabledProviders();
    ResponseUtil.success(res, { providers }, 'Available authentication providers');
  } catch (error) {
    logger.error('Error getting auth providers:', error);
    ResponseUtil.internalError(res, 'Failed to get authentication providers');
  }
});

// Check authentication status
router.get('/status', (req: Request, res: Response) => {
  try {
    const isAuthenticated = req.isAuthenticated && req.isAuthenticated();
    ResponseUtil.success(
      res,
      {
        authenticated: isAuthenticated,
        user: isAuthenticated ? req.user : null,
      },
      'Authentication status'
    );
  } catch (error) {
    logger.error('Error checking auth status:', error);
    ResponseUtil.internalError(res, 'Failed to check authentication status');
  }
});

// Logout
router.post('/logout', (req: Request, res: Response) => {
  try {
    req.logout((err) => {
      if (err) {
        logger.error('Logout error:', err);
        return ResponseUtil.internalError(res, 'Failed to logout');
      }

      // Clear session
      req.session.destroy((sessionErr) => {
        if (sessionErr) {
          logger.error('Session destruction error:', sessionErr);
        }
      });

      ResponseUtil.success(res, null, 'Logged out successfully');
    });
  } catch (error) {
    logger.error('Logout error:', error);
    ResponseUtil.internalError(res, 'Failed to logout');
  }
});

// PingFed OAuth routes (only enabled if PingFed is configured)
if (authManager.isPingFedEnabled()) {
  // PingFed OAuth login
  router.get('/pingfed', async (req: Request, res: Response) => {
    try {
      const state = (req.query.state as string) || Math.random().toString(36).substring(7);
      const authUrl = authManager.getPingFedProvider().getAuthorizationUrl(state);
      res.redirect(authUrl);
    } catch (error) {
      logger.error('PingFed OAuth initiation error:', error);
      res.redirect(`${config.urls.frontend}?auth=error`);
    }
  });

  // PingFed OAuth callback
  router.get('/pingfed/callback', async (req: Request, res: Response) => {
    const startTime = Date.now();
    let timeoutId: NodeJS.Timeout | null = null;
    let isCompleted = false;

    try {
      logger.info('PingFed OAuth callback received', { state: req.query.state });

      // Set a 30-second timeout for the entire callback process
      const timeoutPromise = new Promise((_, reject) => {
        timeoutId = setTimeout(() => {
          reject(new Error('Authentication callback timeout after 30 seconds'));
        }, 30000);
      });

      const authPromise = (async () => {
        const code = req.query.code as string;
        const state = req.query.state as string;

        if (!code) {
          logger.error('No code received in PingFed callback');
          return res.redirect(`${config.urls.frontend}?auth=error`);
        }

        // Handle the callback using PingFed provider
        logger.info('Starting PingFed authentication process');
        const user = await authManager.getPingFedProvider().handleCallback(code, state);
        logger.info('PingFed authentication completed successfully');

        // Set user in session
        return new Promise<void>((resolve, reject) => {
          req.login(user, (err) => {
            if (err) {
              logger.error('Error setting user in session:', err);
              reject(err);
            } else {
              logger.info('User session created successfully');
              resolve();
            }
          });
        });
      })();

      // Race between authentication and timeout
      await Promise.race([authPromise, timeoutPromise]);

      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      if (!isCompleted) {
        isCompleted = true;
        const duration = Date.now() - startTime;
        logger.info(`PingFed authentication completed in ${duration}ms`);
        res.redirect(`${config.urls.frontend}?auth=success`);
      }
    } catch (error) {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      if (!isCompleted) {
        isCompleted = true;
        const duration = Date.now() - startTime;
        logger.error('PingFed auth callback error:', {
          error: error instanceof Error ? error.message : String(error),
          duration,
          stack: error instanceof Error ? error.stack : undefined,
        });

        // Always redirect to prevent hanging
        try {
          res.redirect(`${config.urls.frontend}?auth=error`);
        } catch (redirectError) {
          logger.error('Error during error redirect:', redirectError);
        }
      }
    }
  });
}

export { router as authRoutes };
