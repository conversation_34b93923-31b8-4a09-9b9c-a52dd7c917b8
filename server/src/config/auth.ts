import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env file first
dotenv.config({ path: path.join(__dirname, '../../../.env') });

export type AuthProvider = 'github' | 'azure' | 'both';

export interface AuthConfig {
  provider: AuthProvider;
  sessionSecret: string;
  jwtSecret: string;
  jwtRefreshSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  github: {
    enabled: boolean;
    clientId: string;
    clientSecret: string;
    callbackURL: string;
  };
  azure: {
    enabled: boolean;
    clientId: string;
    clientSecret: string;
    tenantId: string;
    callbackURL: string;
  };
}

export const authConfig: AuthConfig = {
  provider: (process.env.AUTH_PROVIDER as AuthProvider) || 'github',
  sessionSecret: process.env.SESSION_SECRET || 'engineering-excellence-session-secret',
  jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key',
  jwtRefreshSecret: process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
  jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  
  github: {
    enabled: ['github', 'both'].includes(process.env.AUTH_PROVIDER || 'github'),
    clientId: process.env.GITHUB_CLIENT_ID || '',
    clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
    callbackURL: process.env.GITHUB_CALLBACK_URL || `${process.env.BACKEND_URL || 'http://localhost:3002'}/api/auth/github/callback`
  },
  
  azure: {
    enabled: ['azure', 'both'].includes(process.env.AUTH_PROVIDER || 'github'),
    clientId: process.env.AZURE_CLIENT_ID || '',
    clientSecret: process.env.AZURE_CLIENT_SECRET || '',
    tenantId: process.env.AZURE_TENANT_ID || '',
    callbackURL: process.env.AZURE_CALLBACK_URL || `${process.env.BACKEND_URL || 'http://localhost:3002'}/api/auth/azure/callback`
  }
};

// Debug logging (remove in production)
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Auth Config Debug:', {
    authProvider: process.env.AUTH_PROVIDER,
    githubClientId: process.env.GITHUB_CLIENT_ID ? '✅ Set' : '❌ Missing',
    githubClientSecret: process.env.GITHUB_CLIENT_SECRET ? '✅ Set' : '❌ Missing',
    githubEnabled: authConfig.github.enabled,
    azureEnabled: authConfig.azure.enabled
  });
}

// Validation function to ensure required config is present
export const validateAuthConfig = (): void => {
  const errors: string[] = [];

  if (authConfig.github.enabled) {
    if (!authConfig.github.clientId) errors.push('GITHUB_CLIENT_ID is required when GitHub auth is enabled');
    if (!authConfig.github.clientSecret) errors.push('GITHUB_CLIENT_SECRET is required when GitHub auth is enabled');
  }

  if (authConfig.azure.enabled) {
    if (!authConfig.azure.clientId) errors.push('AZURE_CLIENT_ID is required when Azure AD auth is enabled');
    if (!authConfig.azure.clientSecret) errors.push('AZURE_CLIENT_SECRET is required when Azure AD auth is enabled');
    if (!authConfig.azure.tenantId) errors.push('AZURE_TENANT_ID is required when Azure AD auth is enabled');
  }

  if (!authConfig.github.enabled && !authConfig.azure.enabled) {
    errors.push('At least one authentication provider must be enabled');
  }

  if (errors.length > 0) {
    throw new Error(`Authentication configuration errors:\n${errors.join('\n')}`);
  }
}; 