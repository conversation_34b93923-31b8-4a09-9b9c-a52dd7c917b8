import dotenv from 'dotenv';
import path from 'path';

// Load environment variables from root .env file first
dotenv.config({ path: path.join(__dirname, '../../../.env') });

export type AuthProvider = 'pingfed';

export interface AuthConfig {
  provider: AuthProvider;
  sessionSecret: string;
  jwtSecret: string;
  jwtRefreshSecret: string;
  jwtExpiresIn: string;
  jwtRefreshExpiresIn: string;
  pingfed: {
    enabled: boolean;
    clientId: string;
    clientSecret: string;
    issuer: string;
    authorizationEndpoint: string;
    tokenEndpoint: string;
    userInfoEndpoint: string;
    logoutEndpoint: string;
    introspectionEndpoint: string;
    callbackURL: string;
    scopes: string[];
  };
}

export const authConfig: AuthConfig = {
  provider: 'pingfed',
  sessionSecret: process.env.SESSION_SECRET || 'engineering-excellence-session-secret',
  jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key',
  jwtRefreshSecret: process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret',
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '1h',
  jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',

  pingfed: {
    enabled: true,
    clientId: process.env.PINGFED_CLIENT_ID || '',
    clientSecret: process.env.PINGFED_CLIENT_SECRET || '',
    issuer: 'https://pfedcert.wal-mart.com/',
    authorizationEndpoint:
      process.env.PINGFED_AUTHORIZATION_URL ||
      'https://pfedcert.wal-mart.com/as/authorization.oauth2',
    tokenEndpoint: process.env.PINGFED_TOKEN_URL || 'https://pfedcert.wal-mart.com/as/token.oauth2',
    userInfoEndpoint:
      process.env.PINGFED_USERINFO_URL || 'https://pfedcert.wal-mart.com/idp/userinfo.openid',
    logoutEndpoint: 'https://pfedcert.wal-mart.com/as/revoke_token.oauth2',
    introspectionEndpoint: 'https://pfedcert.wal-mart.com/as/introspect.oauth2',
    callbackURL:
      process.env.PINGFED_CALLBACK_URL ||
      `${process.env.BACKEND_URL || 'http://localhost:3001'}/api/auth/pingfed/callback`,
    scopes: ['openid', 'full'],
  },
};

// Debug logging (remove in production)
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Auth Config Debug:', {
    authProvider: 'pingfed',
    pingfedClientId: process.env.PINGFED_CLIENT_ID ? '✅ Set' : '❌ Missing',
    pingfedClientSecret: process.env.PINGFED_CLIENT_SECRET ? '✅ Set' : '❌ Missing',
    pingfedEnabled: authConfig.pingfed.enabled,
  });
}

// Validation function to ensure required config is present
export const validateAuthConfig = (): void => {
  const errors: string[] = [];

  if (authConfig.pingfed.enabled) {
    if (!authConfig.pingfed.clientId)
      errors.push('PINGFED_CLIENT_ID is required when PingFed auth is enabled');
    if (!authConfig.pingfed.clientSecret)
      errors.push('PINGFED_CLIENT_SECRET is required when PingFed auth is enabled');
  }

  if (errors.length > 0) {
    throw new Error(`Authentication configuration errors:\n${errors.join('\n')}`);
  }
};
