import { Release, Team } from './database';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Common Entity Types
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// User Types
export interface User extends BaseEntity {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  authProvider: AuthProvider;
  
  // GitHub OAuth fields
  githubId?: string;
  username?: string;
  displayName?: string;
  avatar?: string;
  profileUrl?: string;
  
  // Azure AD fields
  azureId?: string;
  upn?: string; // User Principal Name
  tenantId?: string;
  jobTitle?: string;
  department?: string;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  VIEWER = 'viewer'
}

export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  role?: UserRole;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
}

// Auth provider enum
export enum AuthProvider {
  GITHUB = 'github',
  AZURE = 'azure'
}

// Profile data from external providers
export interface GitHubProfile {
  id: string;
  username: string;
  displayName: string;
  emails: Array<{
    value: string;
    verified: boolean;
  }>;
  photos: Array<{
    value: string;
  }>;
  profileUrl: string;
}

export interface AzureProfile {
  id: string;
  displayName: string;
  mail: string;
  userPrincipalName: string;
  givenName: string;
  surname: string;
  jobTitle?: string;
  department?: string;
}

// GitHub OAuth User (for creating from GitHub profile)
export interface GitHubUser {
  id: string;
  githubId: string;
  username: string;
  displayName: string;
  email: string;
  avatar: string;
  profileUrl: string;
  createdAt: string;
}

// Error Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError {
  code: string;
  message: string;
  details?: ValidationError[];
}

// Export the database types
export { Release, Team };

// Query parameters
export interface QueryParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  filter?: Record<string, any>;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}
