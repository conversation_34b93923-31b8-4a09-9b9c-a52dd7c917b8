{"name": "@engineering-excellence/server", "version": "1.0.0", "description": "Node.js server for Engineering Excellence application", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@azure/cosmos": "^4.4.1", "@azure/identity": "^4.10.1", "@azure/keyvault-secrets": "^4.10.0", "@azure/msal-node": "^3.6.0", "@types/axios": "^0.9.36", "@types/js-yaml": "^4.0.9", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.13.3", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "passport": "^0.7.0", "passport-github2": "^0.1.12", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.7", "@types/morgan": "^1.9.9", "@types/node": "^20.19.2", "@types/passport": "^1.0.17", "@types/passport-github2": "^1.2.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.7.1", "@typescript-eslint/parser": "^7.7.1", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}