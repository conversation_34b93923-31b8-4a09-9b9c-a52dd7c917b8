teamName: "SAMS E-Com iOS"

metadata:
  # Platform identification
  platform: "ios"
  platform_type: "mobile"

  # Platform-specific configuration
  github_repo: "https://gecgithub01.walmart.com/walmart-ios/glass-app"
  repo_default_branch: "development"
  looper_url: "https://dx.walmart.com/proxy/nextgenci/v1/nextgen-api"
  looper_team_id: "34b26a76-71c0-40f0-9427-db160858ee62"
  sonarqube_url: "https://sonar.looper.prod.walmartlabs.com"
  sonarqube_project_id: "glass-ios"

  # Team communication
  slack_webhook: "https://hooks.slack.com/services/XYZ/ABC/12345"

  # ServiceNow integration
  serviceNowId: "Sams Tech - Mobile applications"

roles:
  functional_qa:
    # Sub-teams under Functional QA and their authorized approvers (QA engineers)
    plp:
      - "<EMAIL>"
      - "<EMAIL>"
    platform:
      - "<EMAIL>"
      - "<EMAIL>"
    home:
      - "<EMAIL>"
      - "<EMAIL>"
    pdp:
      - "<EMAIL>"
      - "<EMAIL>"
    cart:
      - "<EMAIL>"
      - "<EMAIL>"
    checkout:
      - "<EMAIL>"
      - "<EMAIL>"
    curbside:
      - "<EMAIL>"
      - "<EMAIL>"
    sng & fuel:
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
    membership:
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
    account:
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
  e2e_qa:
    # End-to-end QA team approvers (integration testing sign-off)
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  release_devops:
    # Engineering leadership approvers (e.g., Tech Leads, Managers, Directors)
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
  pcf_approvers:
    # Post Code Freeze request approvers (typically senior engineers and leads)
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"
    - "<EMAIL>"

release_types:
  minor:
    signoff_order:
      - functional_qa
      - e2e_qa
      - release_devops
    enforcement:
      functional_qa:
        require_all_subteams: true
        min_signoffs_per_team: 1
      e2e_qa:
        min_signoffs: 1
      release_devops:
        min_signoffs: 2
      blocking_conditions:
        - condition: "no_open_blockers"
          description: "All blocker-severity bugs must be closed before release"
  major:
    signoff_order:
      - functional_qa
      - e2e_qa
      - release_devops
    enforcement:
      functional_qa:
        require_all_subteams: true
        min_signoffs_per_team: 1
      e2e_qa:
        min_signoffs: 1
      release_devops:
        min_signoffs: 2
      blocking_conditions:
        - condition: "no_open_blockers"
          description: "All blocker-severity bugs must be closed before release"
  patch:
    signoff_order:
      - functional_qa
      - e2e_qa
      - release_devops
    enforcement:
      functional_qa:
        require_all_subteams: true
        min_signoffs_per_team: 1
      e2e_qa:
        min_signoffs: 1
      release_devops:
        min_signoffs: 2
      blocking_conditions:
        - condition: "no_open_blockers"
          description: "All blocker-severity bugs must be closed before release"
  hotfix:
    signoff_order:
      - functional_qa
      - release_devops
    enforcement:
      functional_qa:
        require_all_subteams: false
        min_total_signoffs: 1
      release_devops:
        min_signoffs: 1
