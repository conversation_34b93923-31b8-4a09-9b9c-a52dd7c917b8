import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '../../utils';
import { useAuth } from '../../hooks/useAuth';
import { getAuthProviderConfig } from '../../config/auth';
import AuthIcon from '../ui/AuthIcon';
import Avatar from '../ui/Avatar';

interface SidebarItem {
  id: string;
  icon: React.ReactNode;
  label: string;
  path: string;
}

const Sidebar: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const {
    user,
    isLoading,
    isAuthenticated,
    signIn,
    signOut,
    providers,
    isSigningIn,
    isSigningOut,
  } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Get auth provider config from server response (first available provider)
  const currentAuthProvider =
    providers.length > 0
      ? getAuthProviderConfig(providers[0].name.toLowerCase())
      : getAuthProviderConfig('generic');

  const sidebarItems: SidebarItem[] = [
    {
      id: 'dashboard',
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
        </svg>
      ),
      label: 'Dashboard',
      path: '/dashboard',
    },
    {
      id: 'releases',
      icon: (
        <svg className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
          {/* Package/Box base - smaller */}
          <rect x="5" y="18" width="14" height="4" rx="1" opacity="0.2" />
          <path d="M5 19h14v2a1 1 0 01-1 1H6a1 1 0 01-1-1v-2z" opacity="0.4" />

          {/* Much larger rocket body */}
          <path d="M12 1L19 7V17L12 18L5 17V7L12 1Z" />

          {/* Large, clear rocket window */}
          <circle cx="12" cy="9" r="2.2" fill="white" />

          {/* Bigger, more prominent fins */}
          <path d="M5 11L2 14V16L5 14V11Z" />
          <path d="M19 11L22 14V16L19 14V11Z" />

          {/* Small exhaust detail */}
          <path d="M10 18v2l2 1 2-1v-2" opacity="0.5" />
        </svg>
      ),
      label: 'Releases',
      path: '/releases',
    },
    {
      id: 'environments',
      icon: (
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          strokeWidth="1.5"
        >
          {/* Server rack frame */}
          <rect
            x="3"
            y="3"
            width="18"
            height="18"
            rx="2"
            stroke="currentColor"
            strokeWidth="1.5"
            fill="none"
          />

          {/* Server units - clean horizontal lines */}
          <rect x="5" y="6" width="14" height="2.5" rx="0.5" fill="currentColor" opacity="0.8" />
          <rect x="5" y="10" width="14" height="2.5" rx="0.5" fill="currentColor" opacity="0.8" />
          <rect x="5" y="14" width="14" height="2.5" rx="0.5" fill="currentColor" opacity="0.8" />

          {/* Status indicators - modern dots */}
          <circle cx="7" cy="7.25" r="0.4" fill="white" />
          <circle cx="8.5" cy="7.25" r="0.4" fill="white" opacity="0.7" />
          <circle cx="7" cy="11.25" r="0.4" fill="white" />
          <circle cx="8.5" cy="11.25" r="0.4" fill="white" opacity="0.7" />
          <circle cx="7" cy="15.25" r="0.4" fill="white" />
          <circle cx="8.5" cy="15.25" r="0.4" fill="white" opacity="0.7" />

          {/* Network ports - right side */}
          <rect
            x="16.5"
            y="6.75"
            width="1.5"
            height="1"
            rx="0.2"
            fill="currentColor"
            opacity="0.6"
          />
          <rect
            x="16.5"
            y="10.75"
            width="1.5"
            height="1"
            rx="0.2"
            fill="currentColor"
            opacity="0.6"
          />
          <rect
            x="16.5"
            y="14.75"
            width="1.5"
            height="1"
            rx="0.2"
            fill="currentColor"
            opacity="0.6"
          />

          {/* Ventilation grilles - subtle detail */}
          <line x1="11" y1="6.5" x2="15" y2="6.5" stroke="white" strokeWidth="0.3" opacity="0.5" />
          <line x1="11" y1="7.5" x2="15" y2="7.5" stroke="white" strokeWidth="0.3" opacity="0.5" />
          <line
            x1="11"
            y1="10.5"
            x2="15"
            y2="10.5"
            stroke="white"
            strokeWidth="0.3"
            opacity="0.5"
          />
          <line
            x1="11"
            y1="11.5"
            x2="15"
            y2="11.5"
            stroke="white"
            strokeWidth="0.3"
            opacity="0.5"
          />
          <line
            x1="11"
            y1="14.5"
            x2="15"
            y2="14.5"
            stroke="white"
            strokeWidth="0.3"
            opacity="0.5"
          />
          <line
            x1="11"
            y1="15.5"
            x2="15"
            y2="15.5"
            stroke="white"
            strokeWidth="0.3"
            opacity="0.5"
          />
        </svg>
      ),
      label: 'Environments',
      path: '/environments',
    },
    {
      id: 'help',
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h1a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h8a1 1 0 100-2H6z"
            clipRule="evenodd"
          />
          <circle cx="10" cy="8" r="1" />
          <path d="M9.5 9.5h1v1h-1z" />
        </svg>
      ),
      label: 'Help',
      path: '/help',
    },
  ];

  const isActive = (path: string) => location.pathname === path;

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/auth');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <>
      {/* Global Auth Loading Overlay */}
      {(isSigningIn || isSigningOut) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-xl p-6 shadow-2xl flex flex-col items-center space-y-4">
            <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900">
                {isSigningIn ? 'Signing You In' : 'Signing You Out'}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {isSigningIn ? 'Redirecting to authentication...' : 'Please wait...'}
              </p>
            </div>
          </div>
        </div>
      )}

      <div
        className={cn(
          'bg-white border-r border-gray-200 h-screen flex flex-col transition-all duration-300 shadow-lg relative',
          isExpanded ? 'w-64' : 'w-16'
        )}
        onMouseEnter={() => setIsExpanded(true)}
        onMouseLeave={() => setIsExpanded(false)}
      >
        {/* Logo/Brand Area */}
        <div className="p-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center justify-center">
            <div className="relative">
              {/* Main logo container with enhanced styling */}
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg transform transition-all duration-300 hover:scale-105">
                {/* Engineering Excellence Icon */}
                <svg
                  className="w-7 h-7 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  {/* Gear outer ring with teeth */}
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.8}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />

                  {/* Inner circle */}
                  <circle cx="12" cy="12" r="4" strokeWidth={1.8} />

                  {/* Checkmark */}
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2.2}
                    d="M9.5 12l1.5 1.5 3-3"
                  />
                </svg>
                {/* Subtle glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl opacity-0 hover:opacity-20 transition-opacity duration-300"></div>
              </div>
            </div>
            {isExpanded && (
              <div className="ml-3 overflow-hidden">
                <div className="flex flex-col">
                  <span className="font-bold text-gray-900 text-lg tracking-tight leading-tight whitespace-nowrap">
                    Engineering
                  </span>
                  <span className="font-bold text-gray-900 text-lg tracking-tight leading-tight whitespace-nowrap">
                    Excellence
                  </span>
                </div>
                {/* Subtle underline accent */}
                <div className="w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-1 transform origin-left transition-transform duration-300"></div>
              </div>
            )}
          </div>
        </div>

        {/* Navigation Items */}
        <div className="flex-1 py-6">
          <nav className="space-y-2 px-3">
            {sidebarItems.map((item, index) => {
              const colors = [
                'bg-green-100 text-green-600',
                'bg-purple-100 text-purple-600',
                'bg-orange-100 text-orange-600',
                'bg-blue-100 text-blue-600',
              ];

              return (
                <Link
                  key={item.id}
                  to={item.path}
                  className={cn(
                    'flex items-center px-3 py-3 rounded-xl text-sm font-medium transition-all duration-200 group relative',
                    isExpanded ? 'justify-start' : 'justify-center',
                    isActive(item.path)
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  )}
                >
                  <div
                    className={cn(
                      'w-10 h-10 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-200',
                      isActive(item.path)
                        ? 'bg-blue-500 text-white shadow-sm'
                        : colors[index] + ' hover:bg-opacity-80'
                    )}
                  >
                    {item.icon}
                  </div>

                  {isExpanded && (
                    <span className="ml-3 truncate font-medium whitespace-nowrap">
                      {item.label}
                    </span>
                  )}

                  {isActive(item.path) && !isExpanded && (
                    <div className="absolute -right-3 w-1 h-8 bg-blue-500 rounded-l-full"></div>
                  )}

                  {isActive(item.path) && isExpanded && (
                    <div className="absolute right-3 w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  )}
                </Link>
              );
            })}
          </nav>
        </div>

        {/* Bottom Auth Section */}
        <div className="p-3 border-t border-gray-100">
          {!isLoading &&
            (isAuthenticated && user ? (
              // Signed in - show user info and logout
              <div className="space-y-2">
                {/* User Avatar - Show differently for collapsed vs expanded */}
                {!isExpanded ? (
                  // Collapsed: Show just avatar centered
                  <div className="flex justify-center mb-2">
                    <Avatar
                      src={user.avatar}
                      name={user.displayName || user.username || user.firstName || user.email}
                      size="lg"
                      authProvider={user.authProvider}
                      className="hover:ring-2 hover:ring-blue-300 transition-all duration-200 cursor-pointer"
                    />
                  </div>
                ) : (
                  // Expanded: Show avatar with user details
                  <div className="px-3 py-2 rounded-xl hover:bg-gray-50 transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <Avatar
                        src={user.avatar}
                        name={user.displayName || user.username || user.firstName || user.email}
                        size="md"
                        authProvider={user.authProvider}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {user.displayName ||
                            user.username ||
                            `${user.firstName} ${user.lastName}`.trim() ||
                            'User'}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          {user.username ? `@${user.username}` : user.upn ? user.upn : user.email}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <button
                  className={cn(
                    'flex items-center w-full px-3 py-3 rounded-xl text-sm font-medium transition-all duration-200 group relative',
                    isExpanded ? 'justify-start' : 'justify-center',
                    isExpanded
                      ? 'text-gray-600 hover:bg-red-50 hover:text-red-600'
                      : 'hover:bg-gray-50',
                    isSigningOut && 'opacity-75 cursor-not-allowed'
                  )}
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                >
                  <div
                    className={cn(
                      'flex items-center justify-center flex-shrink-0 transition-all duration-200 rounded-full',
                      isExpanded
                        ? 'w-10 h-10 bg-red-100 text-red-600 hover:bg-red-200'
                        : 'w-10 h-10 bg-red-500 hover:bg-red-600 text-white shadow-lg'
                    )}
                  >
                    {isSigningOut ? (
                      <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        />
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        />
                      </svg>
                    ) : (
                      <svg
                        className={isExpanded ? 'w-5 h-5' : 'w-5 h-5'}
                        fill="none"
                        viewBox="0 0 20 20"
                      >
                        {/* Circular border */}
                        <circle
                          cx="10"
                          cy="10"
                          r="8"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          fill="none"
                        />
                        {/* Bracket/Door */}
                        <path
                          d="M7 6V14M7 6H5M7 14H5"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                        />
                        {/* Arrow pointing out of bracket */}
                        <path
                          d="M9 10H15M13 8L15 10L13 12"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                  </div>

                  {isExpanded && (
                    <span className="ml-3 truncate font-medium whitespace-nowrap">
                      {isSigningOut ? 'Signing Out...' : 'Sign Out'}
                    </span>
                  )}
                </button>
              </div>
            ) : (
              // Not signed in - show dynamic auth provider based on server response
              <button
                className={cn(
                  'flex items-center w-full px-3 py-3 rounded-xl text-sm font-medium transition-all duration-200 group relative',
                  isExpanded ? 'justify-start' : 'justify-center',
                  isExpanded ? currentAuthProvider.lightColor : 'hover:bg-gray-50',
                  providers.length === 0 || isSigningIn
                    ? 'opacity-75 cursor-not-allowed'
                    : 'hover:shadow-md'
                )}
                onClick={() => {
                  if (providers.length > 0 && !isSigningIn) {
                    // Use the first available provider or extract provider name from loginUrl
                    const provider = providers[0];
                    const providerName = provider.loginUrl.split('/').pop() || 'pingfed';
                    signIn(providerName);
                  }
                }}
                disabled={providers.length === 0 || isSigningIn}
              >
                <div
                  className={cn(
                    'flex items-center justify-center flex-shrink-0 transition-all duration-200 rounded-full',
                    isExpanded
                      ? 'w-10 h-10 ' + currentAuthProvider.lightColor
                      : 'w-10 h-10 bg-green-500 hover:bg-green-600 text-white shadow-lg'
                  )}
                >
                  <AuthIcon
                    type={isExpanded ? (currentAuthProvider.iconType as any) : 'generic'}
                    className={isExpanded ? 'w-5 h-5' : 'w-5 h-5'}
                  />
                </div>

                {isExpanded && (
                  <span className="ml-3 truncate font-medium whitespace-nowrap">
                    {currentAuthProvider.signInText}
                  </span>
                )}
              </button>
            ))}
        </div>
      </div>
    </>
  );
};

export default Sidebar;
