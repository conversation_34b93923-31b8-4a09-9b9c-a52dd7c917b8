import React from 'react';

interface AuthIconProps {
  type: 'walmart';
  className?: string;
}

const AuthIcon: React.FC<AuthIconProps> = ({ type, className = 'w-5 h-5' }) => {
  switch (type) {
    case 'walmart':
      return (
        <svg className={className} fill="currentColor" viewBox="0 0 20 20">
          {/* Walmart spark icon - simplified version */}
          <path d="M10 2L12.5 7.5L18 7.5L14 11.5L15.5 17L10 14L4.5 17L6 11.5L2 7.5L7.5 7.5L10 2Z" />
        </svg>
      );

    default:
      return (
        <svg className={className} fill="none" viewBox="0 0 20 20">
          {/* Circular border */}
          <circle cx="10" cy="10" r="8" stroke="currentColor" strokeWidth="1.5" fill="none" />
          {/* Bracket/Door */}
          <path
            d="M13 6V14M13 6H15M13 14H15"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
          {/* Arrow pointing into bracket */}
          <path
            d="M5 10H11M9 8L11 10L9 12"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      );
  }
};

export default AuthIcon;
