import { useState, useEffect, useCallback } from 'react';
import { authConfig } from '../config/auth';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  avatar?: string;
  authProvider: 'github' | 'azure' | 'pingfed';
  // GitHub specific
  githubId?: string;
  username?: string;
  profileUrl?: string;
  // Azure specific
  azureId?: string;
  upn?: string;
  jobTitle?: string;
  department?: string;
  createdAt: string;
}

interface AuthProvider {
  name: string;
  loginUrl: string;
  icon: string;
  enabled: boolean;
}

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  providers: AuthProvider[];
  isSigningIn: boolean;
  isSigningOut: boolean;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
    providers: [],
    isSigningIn: false,
    isSigningOut: false,
  });

  const loadProviders = useCallback(async () => {
    try {
      const response = await fetch(`${authConfig.apiBaseUrl}/api/auth/providers`, {
        credentials: 'include',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAuthState((prev) => ({
          ...prev,
          providers: data.data?.providers || [],
        }));
      }
    } catch (error) {
      console.error('Error loading auth providers:', error);
    }
  }, []);

  const checkAuthStatus = useCallback(async () => {
    try {
      const response = await fetch(`${authConfig.apiBaseUrl}/api/auth/status`, {
        credentials: 'include',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAuthState((prev) => ({
          ...prev,
          user: data.data?.user || null,
          isLoading: false,
          isAuthenticated: data.data?.authenticated || false,
        }));
      } else {
        setAuthState((prev) => ({
          ...prev,
          user: null,
          isLoading: false,
          isAuthenticated: false,
        }));
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setAuthState((prev) => ({
        ...prev,
        user: null,
        isLoading: false,
        isAuthenticated: false,
      }));
    }
  }, []);

  const signIn = useCallback((provider: string = 'github') => {
    // Set signing in state
    setAuthState((prev) => ({
      ...prev,
      isSigningIn: true,
    }));

    // Redirect to auth provider
    window.location.href = `${authConfig.apiBaseUrl}/api/auth/${provider}`;
  }, []);

  const signOut = useCallback(async () => {
    // Set signing out state
    setAuthState((prev) => ({
      ...prev,
      isSigningOut: true,
    }));

    // Small delay to show loading state before updating auth state
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Update state to reflect logout
    setAuthState((prev) => ({
      ...prev,
      user: null,
      isLoading: false,
      isAuthenticated: false,
      isSigningOut: true, // Keep showing loading until page refresh
    }));

    try {
      const response = await fetch(`${authConfig.apiBaseUrl}/api/auth/logout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      // Log the result but don't change local state since we already logged out locally
      if (!response.ok) {
        console.warn('Logout API call failed, but user has been logged out locally');
      }
    } catch (error) {
      console.error('Error signing out:', error);
      // State is already updated above, so user is still logged out locally
    } finally {
      // Always refresh the page after logout to ensure UI is completely synchronized
      window.location.reload();
    }
  }, []);

  useEffect(() => {
    loadProviders();
    checkAuthStatus();
  }, [loadProviders, checkAuthStatus]);

  // Check for auth success/error in URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const authStatus = urlParams.get('auth');

    if (authStatus === 'success') {
      // Remove the auth param from URL
      window.history.replaceState({}, document.title, window.location.pathname);
      // Refresh the page to ensure UI is completely synchronized
      window.location.reload();
    } else if (authStatus === 'error') {
      console.error('Authentication failed');
      window.history.replaceState({}, document.title, window.location.pathname);
      // Optionally refresh on error too to clear any stale state
      window.location.reload();
    }
  }, [checkAuthStatus]);

  return {
    ...authState,
    signIn,
    signOut,
    refresh: checkAuthStatus,
  };
};
