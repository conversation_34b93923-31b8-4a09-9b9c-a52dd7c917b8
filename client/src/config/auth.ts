// Auth provider type
export type AuthProvider = 'github' | 'azure' | 'both';

// Client-side auth configuration (server-driven, no env vars needed)
export const authConfig = {
  // Authentication endpoints
  endpoints: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    refresh: '/api/auth/refresh',
    github: '/api/auth/github',
    azure: '/api/auth/azure',
  },
  
  // API base URL
  apiBaseUrl: import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001'
};

// Auth provider configuration (without JSX)
export const authProviderConfig = {
  github: {
    name: 'GitHub',
    iconType: 'github',
    color: 'bg-gray-900 hover:bg-gray-800',
    lightColor: 'bg-gray-100 text-gray-800 hover:bg-gray-900 hover:text-white',
    signInText: 'Sign in with GitHub'
  },
  azure: {
    name: 'Microsoft',
    iconType: 'azure',
    color: 'bg-blue-600 hover:bg-blue-700',
    lightColor: 'bg-blue-100 text-blue-600 hover:bg-blue-600 hover:text-white',
    signInText: 'Sign in with Microsoft'
  },
  microsoft: {
    name: 'Microsoft',
    iconType: 'azure',
    color: 'bg-blue-600 hover:bg-blue-700',
    lightColor: 'bg-blue-100 text-blue-600 hover:bg-blue-600 hover:text-white',
    signInText: 'Sign in with Microsoft'
  },
  generic: {
    name: 'Sign In',
    iconType: 'generic',
    color: 'bg-gray-600 hover:bg-gray-700',
    lightColor: 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800',
    signInText: 'Sign In'
  }
};

// Get auth provider config based on server response
export const getAuthProviderConfig = (providerName: string) => {
  const key = providerName.toLowerCase();
  return authProviderConfig[key as keyof typeof authProviderConfig] || authProviderConfig.generic;
}; 