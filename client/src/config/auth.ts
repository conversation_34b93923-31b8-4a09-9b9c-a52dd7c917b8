// Auth provider type
export type AuthProvider = 'pingfed'

// Client-side auth configuration (server-driven, no env vars needed)
export const authConfig = {
  // Authentication endpoints
  endpoints: {
    login: '/api/auth/login',
    logout: '/api/auth/logout',
    refresh: '/api/auth/refresh',
    pingfed: '/api/auth/pingfed',
  },

  // API base URL
  apiBaseUrl: import.meta.env.VITE_BACKEND_URL || 'http://localhost:3001',
};

// Auth provider configuration (without JSX)
export const authProviderConfig = {
  pingfed: {
    name: 'PingFed',
    iconType: 'walmart',
    color: 'bg-blue-600 hover:bg-blue-700',
    lightColor: 'bg-blue-100 text-blue-600 hover:bg-blue-600 hover:text-white',
    signInText: 'Sign in with PingFed',
  },
};

// Get auth provider config based on server response
export const getAuthProviderConfig = (providerName: string) => {
  const key = providerName.toLowerCase();
  return authProviderConfig[key as keyof typeof authProviderConfig] || authProviderConfig.pingfed;
};
